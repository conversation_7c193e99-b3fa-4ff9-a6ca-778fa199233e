#!/bin/bash

# Portfolio Infrastructure Cost Management Script
# This script helps you quickly switch between different cost configurations

set -e

echo "🎯 Portfolio Infrastructure Cost Management"
echo "=========================================="

# Function to apply configuration
apply_config() {
    local config_name=$1
    local environments=$2
    local frontend=$3
    local description=$4
    
    echo "📋 Applying: $config_name"
    echo "   Description: $description"
    echo "   Environments: $environments"
    echo "   Frontend: $frontend"
    echo ""
    
    # Create temporary tfvars file
    cat > temp-cost-config.tfvars << EOF
enabled_environments = $environments
enable_frontend = $frontend
enable_spot_instances = true
spot_max_price = "0.05"
enable_lifecycle_policy = true
EOF
    
    echo "🚀 Running terraform apply..."
    terraform apply -var-file="temp-cost-config.tfvars" -auto-approve
    
    # Clean up
    rm temp-cost-config.tfvars
    
    echo "✅ Configuration applied successfully!"
    echo ""
}

# Show current status
echo "📊 Current Infrastructure Status:"
terraform output -json | jq -r '.cost_optimization_summary.value | "Enabled Environments: \(.enabled_environments | join(", "))\nFrontend Enabled: \(.frontend_enabled)\nEstimated Cost: \(.estimated_monthly_cost.total_estimated)"'
echo ""

echo "💰 Available Cost Configurations:"
echo ""
echo "1. MAXIMUM SAVINGS - S3 only (~$1-5/month)"
echo "   No backend environments, no frontend"
echo ""
echo "2. MINIMAL SETUP - Production backend only (~$25-75/month)"
echo "   Production backend only, no frontend"
echo ""
echo "3. STANDARD SETUP - Production + Frontend (~$40-100/month)"
echo "   Production backend + frontend (current default)"
echo ""
echo "4. DEVELOPMENT SETUP - All environments (~$50-135/month)"
echo "   All environments + frontend with spot instances"
echo ""
echo "5. CUSTOM - Specify your own configuration"
echo ""

read -p "Choose configuration (1-5): " choice

case $choice in
    1)
        apply_config "MAXIMUM SAVINGS" "[]" "false" "S3 storage only - minimum cost"
        ;;
    2)
        apply_config "MINIMAL SETUP" "[\"prod\"]" "false" "Production backend only"
        ;;
    3)
        apply_config "STANDARD SETUP" "[\"prod\"]" "true" "Production backend + frontend"
        ;;
    4)
        apply_config "DEVELOPMENT SETUP" "[\"dev\", \"stage\", \"prod\"]" "true" "All environments with spot instances"
        ;;
    5)
        echo "🔧 Custom Configuration:"
        echo "Available environments: dev, stage, prod"
        read -p "Enter environments (comma-separated, e.g., prod,dev): " env_input
        read -p "Enable frontend? (true/false): " frontend_input
        
        # Convert comma-separated to array format
        IFS=',' read -ra ENV_ARRAY <<< "$env_input"
        env_formatted="["
        for i in "${ENV_ARRAY[@]}"; do
            env_formatted+="\"$(echo $i | xargs)\","
        done
        env_formatted="${env_formatted%,}]"
        
        apply_config "CUSTOM SETUP" "$env_formatted" "$frontend_input" "Custom configuration"
        ;;
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

echo "🎉 Cost management complete!"
echo ""
echo "📊 New Infrastructure Status:"
terraform output cost_optimization_summary
