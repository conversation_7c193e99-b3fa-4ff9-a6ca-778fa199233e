#!/bin/bash

# S3 Cleanup Script for Portfolio Infrastructure
# This script helps you clean up S3 storage and manage build versions

set -e

BUCKET_NAME="portfolio-app-cicd-artifacts"

echo "🗂️ Portfolio S3 Storage Cleanup Tool"
echo "===================================="

# Check if bucket exists
if ! aws s3 ls "s3://$BUCKET_NAME" >/dev/null 2>&1; then
    echo "❌ S3 bucket '$BUCKET_NAME' not found or not accessible"
    echo "   Make sure you have AWS CLI configured and the bucket exists"
    exit 1
fi

echo "📊 Current S3 Storage Usage:"
aws s3 ls s3://$BUCKET_NAME --recursive --human-readable --summarize

echo ""
echo "🧹 Available Cleanup Options:"
echo ""
echo "1. Clean up old versions (keep latest 3 per folder)"
echo "2. Delete specific environment folder (dev/stage/prod)"
echo "3. Delete all frontend versions (keep latest 3)"
echo "4. Delete incomplete multipart uploads"
echo "5. Delete ALL S3 data (DANGER - will break pipelines)"
echo "6. Show detailed storage breakdown"
echo "7. Exit"
echo ""

read -p "Choose option (1-7): " choice

case $choice in
    1)
        echo "🔄 Cleaning up old versions (keeping latest 3)..."
        
        # List all objects and their versions, keep only latest 3 per key
        aws s3api list-object-versions --bucket $BUCKET_NAME --query 'Versions[?IsLatest==`false`]' --output text | \
        while read version_id key; do
            if [ ! -z "$version_id" ]; then
                echo "Deleting old version: $key ($version_id)"
                aws s3api delete-object --bucket $BUCKET_NAME --key "$key" --version-id "$version_id"
            fi
        done
        
        echo "✅ Old versions cleanup completed"
        ;;
    2)
        echo "Available environment folders:"
        aws s3 ls s3://$BUCKET_NAME/ | grep "PRE" | awk '{print $2}' | sed 's/\///'
        echo ""
        read -p "Enter environment to delete (dev/stage/prod): " env
        
        if [[ "$env" =~ ^(dev|stage|prod)$ ]]; then
            echo "⚠️  WARNING: This will delete ALL builds for $env environment"
            read -p "Are you sure? (yes/no): " confirm
            if [ "$confirm" = "yes" ]; then
                echo "🗑️  Deleting $env environment folder..."
                aws s3 rm s3://$BUCKET_NAME/$env/ --recursive
                echo "✅ $env environment folder deleted"
            else
                echo "❌ Operation cancelled"
            fi
        else
            echo "❌ Invalid environment. Use: dev, stage, or prod"
        fi
        ;;
    3)
        echo "🗑️  Cleaning up frontend versions (keeping latest 3)..."
        
        # List frontend versions and delete old ones
        aws s3 ls s3://$BUCKET_NAME/frontend-versions/ | sort -k1,2 | head -n -3 | \
        while read date time size filename; do
            if [ ! -z "$filename" ]; then
                echo "Deleting old frontend version: $filename"
                aws s3 rm s3://$BUCKET_NAME/frontend-versions/$filename
            fi
        done
        
        echo "✅ Frontend versions cleanup completed"
        ;;
    4)
        echo "🧹 Cleaning up incomplete multipart uploads..."
        
        aws s3api list-multipart-uploads --bucket $BUCKET_NAME --query 'Uploads[].{Key:Key,UploadId:UploadId}' --output text | \
        while read key upload_id; do
            if [ ! -z "$upload_id" ]; then
                echo "Aborting incomplete upload: $key ($upload_id)"
                aws s3api abort-multipart-upload --bucket $BUCKET_NAME --key "$key" --upload-id "$upload_id"
            fi
        done
        
        echo "✅ Incomplete uploads cleanup completed"
        ;;
    5)
        echo "⚠️  DANGER: This will delete ALL S3 data and break CI/CD pipelines!"
        echo "   This is equivalent to setting enable_s3_storage = false"
        echo ""
        read -p "Type 'DELETE_ALL_S3_DATA' to confirm: " confirm
        
        if [ "$confirm" = "DELETE_ALL_S3_DATA" ]; then
            echo "🗑️  Deleting ALL S3 data..."
            
            # Delete all object versions first
            aws s3api list-object-versions --bucket $BUCKET_NAME --query 'Versions[].{Key:Key,VersionId:VersionId}' --output text | \
            while read key version_id; do
                if [ ! -z "$version_id" ]; then
                    aws s3api delete-object --bucket $BUCKET_NAME --key "$key" --version-id "$version_id"
                fi
            done
            
            # Delete all delete markers
            aws s3api list-object-versions --bucket $BUCKET_NAME --query 'DeleteMarkers[].{Key:Key,VersionId:VersionId}' --output text | \
            while read key version_id; do
                if [ ! -z "$version_id" ]; then
                    aws s3api delete-object --bucket $BUCKET_NAME --key "$key" --version-id "$version_id"
                fi
            done
            
            echo "✅ ALL S3 data deleted"
            echo "⚠️  Note: You'll need to run 'terraform apply -var=\"enable_s3_storage=false\"' to update infrastructure"
        else
            echo "❌ Operation cancelled"
        fi
        ;;
    6)
        echo "📊 Detailed Storage Breakdown:"
        echo ""
        echo "Environment folders:"
        for env in dev stage prod; do
            size=$(aws s3 ls s3://$BUCKET_NAME/$env/ --recursive --summarize 2>/dev/null | grep "Total Size" | awk '{print $3, $4}' || echo "0 Bytes")
            count=$(aws s3 ls s3://$BUCKET_NAME/$env/ --recursive 2>/dev/null | wc -l || echo "0")
            echo "  $env: $size ($count files)"
        done
        
        echo ""
        echo "Frontend versions:"
        size=$(aws s3 ls s3://$BUCKET_NAME/frontend-versions/ --recursive --summarize 2>/dev/null | grep "Total Size" | awk '{print $3, $4}' || echo "0 Bytes")
        count=$(aws s3 ls s3://$BUCKET_NAME/frontend-versions/ --recursive 2>/dev/null | wc -l || echo "0")
        echo "  frontend-versions: $size ($count files)"
        
        echo ""
        echo "Object versions:"
        versions=$(aws s3api list-object-versions --bucket $BUCKET_NAME --query 'length(Versions)' --output text 2>/dev/null || echo "0")
        echo "  Total versions: $versions"
        
        echo ""
        echo "Incomplete uploads:"
        incomplete=$(aws s3api list-multipart-uploads --bucket $BUCKET_NAME --query 'length(Uploads)' --output text 2>/dev/null || echo "0")
        echo "  Incomplete uploads: $incomplete"
        ;;
    7)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid option. Please choose 1-7."
        exit 1
        ;;
esac

echo ""
echo "📊 Updated S3 Storage Usage:"
aws s3 ls s3://$BUCKET_NAME --recursive --human-readable --summarize

echo ""
echo "💡 Tips:"
echo "  - Use Terraform variables to control S3 storage automatically"
echo "  - Set max_build_versions = 3 to keep only latest 3 builds"
echo "  - Set enable_s3_storage = false to disable S3 completely"
echo "  - GitHub Actions will continue to work with automatic cleanup"
