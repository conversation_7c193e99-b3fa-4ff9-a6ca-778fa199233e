# 🏗️ Portfolio Infrastructure Architecture

## 📋 **Table of Contents**
- [Overview](#overview)
- [Architecture Diagram](#architecture-diagram)
- [Cost Optimization](#cost-optimization)
- [Environment Management](#environment-management)
- [Upgrade Procedures](#upgrade-procedures)
- [Configuration Constants](#configuration-constants)

## 🎯 **Overview**

This infrastructure provides a cost-optimized, scalable AWS solution for portfolio applications with intelligent environment management. The architecture supports dynamic scaling from $0/month (everything disabled) to full development setup ($50-135/month).

## 🏗️ **Architecture Diagram**

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           AWS PORTFOLIO INFRASTRUCTURE                      │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                          ENVIRONMENT LAYER                             │ │
│  │                                                                         │ │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                │ │
│  │  │     DEV     │    │    STAGE    │    │    PROD     │                │ │
│  │  │ (Optional)  │    │ (Optional)  │    │ (Required)  │                │ │
│  │  │             │    │             │    │             │                │ │
│  │  │ • t2.micro  │    │ • t2.micro  │    │ • t2.micro  │                │ │
│  │  │ • Spot Inst │    │ • Spot Inst │    │ • On-Demand │                │ │
│  │  │ • Max: 1    │    │ • Max: 1    │    │ • Max: 3    │                │ │
│  │  │ • HTTPS     │    │ • HTTPS     │    │ • HTTPS     │                │ │
│  │  └─────────────┘    └─────────────┘    └─────────────┘                │ │
│  │         │                   │                   │                     │ │
│  │         └───────────────────┼───────────────────┘                     │ │
│  │                             │                                         │ │
│  └─────────────────────────────┼─────────────────────────────────────────┘ │
│                                │                                           │
│  ┌─────────────────────────────┼─────────────────────────────────────────┐ │
│  │                    SHARED SERVICES LAYER                             │ │
│  │                             │                                         │ │
│  │  ┌─────────────┐  ┌─────────┴───────┐  ┌─────────────┐              │ │
│  │  │     S3      │  │   CodePipeline  │  │   Route53   │              │ │
│  │  │ (Optional)  │  │   (Conditional) │  │   (HTTPS)   │              │ │
│  │  │             │  │                 │  │             │              │ │
│  │  │ • Lifecycle │  │ • Manual Trigger│  │ • SSL Certs │              │ │
│  │  │ • 3 Builds  │  │ • Auto Deploy  │  │ • Custom DNS│              │ │
│  │  │ • 7d Cleanup│  │ • Notifications │  │ • Wildcard  │              │ │
│  │  └─────────────┘  └─────────────────┘  └─────────────┘              │ │
│  │                                                                       │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                  │ │
│  │  │ CodeBuild   │  │     SNS     │  │   Frontend  │                  │ │
│  │  │ (Conditional)│  │ Notifications│  │ (Optional)  │                  │ │
│  │  │             │  │             │  │             │                  │ │
│  │  │ • GitHub    │  │ • Email     │  │ • React/Node│                  │ │
│  │  │ • Buildspec │  │ • Pipeline  │  │ • Production│                  │ │
│  │  │ • Artifacts │  │ • Status    │  │ • Auto Scale│                  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘                  │ │
│  └─────────────────────────────────────────────────────────────────────┘ │
│                                                                           │
│  ┌─────────────────────────────────────────────────────────────────────┐ │
│  │                        CONTROL LAYER                               │ │
│  │                                                                     │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │ │
│  │  │ Terraform   │  │ Cost Control│  │ Monitoring  │                │ │
│  │  │ Variables   │  │ Flags       │  │ & Alerts    │                │ │
│  │  │             │  │             │  │             │                │ │
│  │  │ • Env List  │  │ • S3 Toggle │  │ • CloudWatch│                │ │
│  │  │ • Frontend  │  │ • Build Cnt │  │ • SNS Alerts│                │ │
│  │  │ • S3 Config │  │ • Cleanup   │  │ • Cost Track│                │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘                │ │
│  └─────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 💰 **Cost Optimization Strategy**

### **Tier 1: Maximum Savings ($0/month)**
```hcl
enabled_environments = []
enable_frontend = false
enable_s3_storage = false
```
- **Use Case**: Project on hold, no active development
- **Resources**: None (everything disabled)
- **Savings**: 100% cost reduction

### **Tier 2: Minimal Production ($25-75/month)**
```hcl
enabled_environments = ["prod"]
enable_frontend = false
enable_s3_storage = true
max_build_versions = 3
```
- **Use Case**: Simple backend API only
- **Resources**: Production backend + S3 storage
- **Savings**: 70% compared to full setup

### **Tier 3: Standard Setup ($75-105/month)**
```hcl
enabled_environments = ["prod"]
enable_frontend = true
enable_s3_storage = true
max_build_versions = 3
```
- **Use Case**: Full portfolio with frontend and backend
- **Resources**: Production backend + frontend + S3
- **Savings**: 40% compared to development setup

### **Tier 4: Development Mode ($50-135/month)**
```hcl
enabled_environments = ["dev", "stage", "prod"]
enable_frontend = true
enable_s3_storage = true
max_build_versions = 3
enable_spot_instances = true
```
- **Use Case**: Active development with all environments
- **Resources**: All environments with spot instances
- **Savings**: 60-90% on dev/stage through spot instances

## 🔄 **Environment Management**

### **Dynamic Environment Control**

The infrastructure supports dynamic environment management through Terraform variables:

```hcl
# Enable specific environments
variable "enabled_environments" {
  description = "List of environments to create"
  type        = list(string)
  default     = ["prod"]  # Start with production only
  
  validation {
    condition = alltrue([
      for env in var.enabled_environments : contains(["dev", "stage", "prod"], env)
    ])
    error_message = "Enabled environments must be one of: dev, stage, prod."
  }
}
```

### **Environment Characteristics**

| **Environment** | **Instance Type** | **Scaling** | **Spot Instances** | **SSL** |
|-----------------|-------------------|-------------|-------------------|---------|
| **Development** | t2.micro | 1-1 | ✅ Yes | ✅ HTTPS |
| **Staging** | t2.micro | 1-1 | ✅ Yes | ✅ HTTPS |
| **Production** | t2.micro | 1-3 | ❌ On-Demand | ✅ HTTPS |
| **Frontend** | t2.micro | 1-2 | ❌ On-Demand | ✅ HTTPS |

## 📈 **Upgrade Procedures**

### **1. Enable Development Environments**

#### **Method A: Configuration File**
```bash
# Edit cost-control.tfvars
enabled_environments = ["dev", "stage", "prod"]
enable_frontend = true
enable_s3_storage = true

# Apply changes
terraform apply -var-file="cost-control.tfvars"
```

#### **Method B: Direct Command**
```bash
terraform apply \
  -var='enabled_environments=["dev","stage","prod"]' \
  -var='enable_frontend=true' \
  -var='enable_s3_storage=true'
```

#### **Method C: Interactive Script**
```bash
./manage-costs.sh
# Choose option 4: DEVELOPMENT SETUP
```

### **2. Scale Up Infrastructure**

#### **Upgrade Instance Types**
```hcl
# In terraform.tfvars
instance_type = "t3.small"  # From t2.micro

# Apply upgrade
terraform apply
```

#### **Increase Auto Scaling**
```hcl
auto_scaling_max_size = {
  dev   = 2
  stage = 2  
  prod  = 5
}
```

#### **Optimize Build Retention**
```hcl
max_build_versions = 5    # Keep more builds
s3_cleanup_days = 14     # Longer retention
```

### **3. Performance Optimization**

#### **Enable Enhanced Monitoring**
```hcl
# Upgrade to enhanced health reporting
health_reporting_system_type = "enhanced"

# Enable detailed CloudWatch metrics
enable_detailed_monitoring = true
```

#### **Database Integration**
```hcl
# Add RDS database
enable_database = true
database_instance_class = "db.t3.micro"
database_engine = "postgres"
```

## ⚙️ **Configuration Constants**

### **Build Retention Constants**
```hcl
# constants.tf (create this file)
locals {
  # Build retention settings
  default_build_retention = 3
  max_build_retention = 10
  min_build_retention = 1
  
  # Cleanup intervals
  s3_cleanup_intervals = {
    aggressive = 1   # 1 day
    standard   = 7   # 7 days  
    relaxed    = 30  # 30 days
  }
  
  # Cost optimization presets
  cost_presets = {
    maximum_savings = {
      enabled_environments = []
      enable_frontend = false
      enable_s3_storage = false
    }
    
    minimal_setup = {
      enabled_environments = ["prod"]
      enable_frontend = false
      enable_s3_storage = true
      max_build_versions = local.default_build_retention
    }
    
    standard_setup = {
      enabled_environments = ["prod"]
      enable_frontend = true
      enable_s3_storage = true
      max_build_versions = local.default_build_retention
    }
    
    development_mode = {
      enabled_environments = ["dev", "stage", "prod"]
      enable_frontend = true
      enable_s3_storage = true
      max_build_versions = local.default_build_retention
      enable_spot_instances = true
    }
  }
}
```

### **Environment-Specific Constants**
```hcl
locals {
  environment_configs = {
    dev = {
      instance_type = "t2.micro"
      min_size = 1
      max_size = 1
      spot_enabled = true
      spot_max_price = "0.05"
    }
    
    stage = {
      instance_type = "t2.micro"
      min_size = 1
      max_size = 1
      spot_enabled = true
      spot_max_price = "0.05"
    }
    
    prod = {
      instance_type = "t2.micro"
      min_size = 1
      max_size = 3
      spot_enabled = false
      spot_max_price = null
    }
  }
}
```

## 🔧 **Quick Commands Reference**

### **Environment Management**
```bash
# Enable all environments
terraform apply -var='enabled_environments=["dev","stage","prod"]'

# Production only
terraform apply -var='enabled_environments=["prod"]'

# Disable all environments
terraform apply -var='enabled_environments=[]'
```

### **Cost Control**
```bash
# Maximum savings
terraform apply -var='enable_s3_storage=false' -var='enable_frontend=false'

# Standard setup
terraform apply -var-file="cost-control.tfvars"

# Development mode
terraform apply -var='enabled_environments=["dev","stage","prod"]' -var='enable_spot_instances=true'
```

### **Build Management**
```bash
# Keep only 3 builds
terraform apply -var='max_build_versions=3'

# Aggressive cleanup (1 day)
terraform apply -var='s3_cleanup_days=1'

# Relaxed cleanup (30 days)
terraform apply -var='s3_cleanup_days=30'
```

---

**💡 Pro Tip**: Use the configuration constants to create standardized deployment presets. This ensures consistent environments and makes scaling decisions easier.
