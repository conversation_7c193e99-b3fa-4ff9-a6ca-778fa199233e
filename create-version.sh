#!/bin/bash

# Manual Version Creation Script
# Usage: ./create-version.sh [major.minor.patch] [environment]

set -e

# Configuration
APP_NAME="portfolio-frontend"
S3_BUCKET="portfolio-app-cicd-artifacts"
S3_PREFIX="frontend-versions"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_usage() {
    echo -e "${BLUE}Manual Version Creation Script${NC}"
    echo ""
    echo "Usage: $0 [version] [environment]"
    echo ""
    echo "Examples:"
    echo "  $0                           # Auto-increment patch version"
    echo "  $0 1.0.5                    # Create specific version"
    echo "  $0 1.1.0 staging            # Create version for staging"
    echo "  $0 2.0.0 production         # Create major version for production"
    echo ""
    echo "Environments: production, staging, development"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi
}

get_next_version() {
    local custom_version=$1
    
    if [[ -n "$custom_version" ]]; then
        echo "$custom_version"
        return
    fi
    
    # Get existing versions from S3
    local existing_versions
    existing_versions=$(aws s3 ls s3://$S3_BUCKET/$S3_PREFIX/ --recursive | \
        grep "${APP_NAME}-v" | \
        grep -o "v[0-9]\+\.[0-9]\+\.[0-9]\+" | \
        sort -V | \
        tail -1)
    
    if [[ -z "$existing_versions" ]]; then
        echo "1.0.1"
        return
    fi
    
    # Extract version numbers
    local version_num=$(echo "$existing_versions" | sed 's/v//')
    local major=$(echo "$version_num" | cut -d. -f1)
    local minor=$(echo "$version_num" | cut -d. -f2)
    local patch=$(echo "$version_num" | cut -d. -f3)
    
    # Increment patch version
    patch=$((patch + 1))
    
    echo "${major}.${minor}.${patch}"
}

validate_version() {
    local version=$1
    
    if [[ ! "$version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        print_error "Invalid version format. Use semantic versioning (e.g., 1.0.1)"
        exit 1
    fi
}

check_version_exists() {
    local version=$1
    local version_file="${APP_NAME}-v${version}-$(date +%Y%m%d).zip"
    
    if aws s3 ls s3://$S3_BUCKET/$S3_PREFIX/$version_file &> /dev/null; then
        print_warning "Version $version already exists for today"
        echo "Do you want to create a new build with the same version? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_info "Version creation cancelled"
            exit 0
        fi
    fi
}

trigger_github_action() {
    local version=$1
    local environment=$2
    
    print_info "This script creates a version placeholder."
    print_info "To build the actual version, you need to:"
    echo ""
    echo "1. Go to your GitHub repository: https://github.com/learning-orgs/portfolio-frontend"
    echo "2. Go to Actions tab"
    echo "3. Select 'Build Frontend Version' workflow"
    echo "4. Click 'Run workflow'"
    echo "5. Select environment: $environment"
    echo ""
    print_info "Or push changes to trigger automatic build"
}

create_version_placeholder() {
    local version=$1
    local environment=$2
    local build_date=$(date +%Y%m%d)
    local version_name="${APP_NAME}-v${version}-${build_date}"
    
    print_info "Creating version placeholder: $version_name"
    
    # Create placeholder metadata
    cat > ${version_name}-placeholder.json << EOF
{
  "version": "$version_name",
  "appName": "$APP_NAME",
  "semanticVersion": "v$version",
  "buildDate": "$build_date",
  "buildTimestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "$environment",
  "status": "placeholder",
  "deploymentReady": false,
  "buildTool": "manual-script",
  "notes": "Version placeholder created manually. Actual build pending."
}
EOF
    
    # Upload placeholder to S3
    aws s3 cp ${version_name}-placeholder.json s3://$S3_BUCKET/$S3_PREFIX/${version_name}-placeholder.json
    
    # Clean up local file
    rm ${version_name}-placeholder.json
    
    print_success "Version placeholder created: $version_name"
    print_info "S3 Location: s3://$S3_BUCKET/$S3_PREFIX/${version_name}-placeholder.json"
}

# Main script
main() {
    local custom_version=$1
    local environment=${2:-"production"}
    
    # Show usage if help requested
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        print_usage
        exit 0
    fi
    
    print_info "Manual Version Creation Tool"
    echo ""
    
    # Checks
    print_info "Performing pre-creation checks..."
    check_aws_cli
    
    # Determine version
    local version
    version=$(get_next_version "$custom_version")
    validate_version "$version"
    
    print_info "Version to create: v$version"
    print_info "Environment: $environment"
    print_info "App name: $APP_NAME"
    
    # Check if version exists
    check_version_exists "$version"
    
    # Confirm creation
    echo ""
    print_warning "Ready to create version placeholder:"
    echo "  Version: v$version"
    echo "  Environment: $environment"
    echo "  App: $APP_NAME"
    echo ""
    echo "Continue? (y/N)"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_info "Version creation cancelled"
        exit 0
    fi
    
    # Create placeholder
    create_version_placeholder "$version" "$environment"
    
    # Show next steps
    echo ""
    trigger_github_action "$version" "$environment"
    
    print_success "Version placeholder created successfully!"
}

# Run main function
main "$@"
