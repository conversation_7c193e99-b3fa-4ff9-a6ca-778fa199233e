# Portfolio App Terraform Outputs

output "environment_urls" {
  description = "Elastic Beanstalk URLs for enabled environments"
  value = {
    for env in var.enabled_environments :
    env => "http://${aws_elastic_beanstalk_environment.app_env[env].cname}"
  }
}

output "custom_domain_urls" {
  description = "Custom domain URLs for backend API"
  value = var.create_custom_domain ? {
    for env in var.enabled_environments :
    env => "https://${var.environment_config[env].subdomain}.${var.domain_name}"
  } : {}
}

output "frontend_url" {
  description = "Frontend URL"
  value = var.enable_frontend ? (var.create_custom_domain ? "https://www.${var.domain_name}" : "http://${aws_elastic_beanstalk_environment.frontend_env[0].cname}") : "Frontend disabled"
}

output "frontend_environment" {
  description = "Frontend Elastic Beanstalk environment name"
  value = var.enable_frontend ? aws_elastic_beanstalk_environment.frontend_env[0].name : "Frontend disabled"
}

output "frontend_pipeline" {
  description = "Frontend CodePipeline name"
  value = var.enable_frontend ? aws_codepipeline.frontend_pipeline[0].name : "Frontend disabled"
}

output "frontend_application" {
  description = "Frontend Elastic Beanstalk application name"
  value = var.enable_frontend ? aws_elastic_beanstalk_application.frontend_app[0].name : "Frontend disabled"
}

# GitHub integration uses OAuth token stored in AWS Secrets Manager
# No CodeStar connection outputs needed

output "pipeline_names" {
  description = "CodePipeline names for manual triggering"
  value = {
    for env in var.enabled_environments :
    env => aws_codepipeline.pipeline[env].name
  }
}

output "s3_bucket" {
  description = "S3 bucket for CI/CD artifacts"
  value       = var.enable_s3_storage ? aws_s3_bucket.artifacts[0].bucket : "S3 storage disabled"
}

output "wildcard_certificate_arn" {
  description = "Wildcard SSL certificate ARN for all environments"
  value = var.create_custom_domain ? aws_acm_certificate.wildcard_cert[0].arn : null
}

output "deployment_summary" {
  description = "Quick deployment reference"
  value = {
    s3_bucket = var.enable_s3_storage ? aws_s3_bucket.artifacts[0].bucket : "S3 disabled"
    pipelines = {
      for env in var.enabled_environments :
      env => aws_codepipeline.pipeline[env].name
    }
    urls = var.create_custom_domain ? {
      for env in var.environments :
      env => "https://${var.environment_config[env].subdomain}.${var.domain_name}"
    } : {
      for env in var.environments :
      env => "http://${aws_elastic_beanstalk_environment.app_env[env].cname}"
    }
    notifications = {
      sns_topic = aws_sns_topic.build_notifications.arn
      email     = var.notification_email
    }
  }
}

# Cost Optimization Information
output "cost_optimization_summary" {
  description = "Summary of cost optimization features enabled"
  value = {
    enabled_environments   = var.enabled_environments
    frontend_enabled      = var.enable_frontend
    s3_storage_enabled    = var.enable_s3_storage
    max_build_versions    = var.max_build_versions
    s3_cleanup_days       = var.s3_cleanup_days
    spot_instances_enabled = var.enable_spot_instances
    spot_max_price        = var.spot_max_price
    lifecycle_policy      = var.enable_lifecycle_policy
    auto_scaling_limits   = var.auto_scaling_max_size
    instance_type         = var.instance_type
    estimated_monthly_cost = {
      dev_environment   = contains(var.enabled_environments, "dev") ? (var.enable_spot_instances ? "$5-15" : "$15-25") : "$0 (disabled)"
      stage_environment = contains(var.enabled_environments, "stage") ? (var.enable_spot_instances ? "$5-15" : "$15-25") : "$0 (disabled)"
      prod_environment  = contains(var.enabled_environments, "prod") ? "$25-75" : "$0 (disabled)"
      frontend         = var.enable_frontend ? "$15-25" : "$0 (disabled)"
      s3_storage       = "$1-5"
      total_estimated  = "${5 + (contains(var.enabled_environments, "dev") ? (var.enable_spot_instances ? 10 : 20) : 0) + (contains(var.enabled_environments, "stage") ? (var.enable_spot_instances ? 10 : 20) : 0) + (contains(var.enabled_environments, "prod") ? 50 : 0) + (var.enable_frontend ? 20 : 0)}-${5 + (contains(var.enabled_environments, "dev") ? (var.enable_spot_instances ? 15 : 25) : 0) + (contains(var.enabled_environments, "stage") ? (var.enable_spot_instances ? 15 : 25) : 0) + (contains(var.enabled_environments, "prod") ? 75 : 0) + (var.enable_frontend ? 25 : 0)}"
    }
    cost_savings = {
      environment_control = "Disable unused environments for maximum savings"
      spot_instances     = var.enable_spot_instances ? "60-90% on dev/stage" : "Not enabled"
      s3_lifecycle       = var.enable_lifecycle_policy ? "30-50% on storage" : "Not enabled"
      auto_scaling       = "Pay only for what you use"
    }
    maximum_cost_savings = {
      prod_only = "~$25-80/month (prod + frontend + S3)"
      prod_only_no_frontend = "~$25-75/month (prod only + S3)"
      all_disabled = "~$1-5/month (S3 storage only)"
    }
  }
}