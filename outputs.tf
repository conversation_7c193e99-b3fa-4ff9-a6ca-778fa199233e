# Portfolio App Terraform Outputs

output "environment_urls" {
  description = "Elastic Beanstalk URLs for each environment"
  value = {
    for env in var.environments :
    env => "http://${aws_elastic_beanstalk_environment.app_env[env].cname}"
  }
}

output "custom_domain_urls" {
  description = "Custom domain URLs for backend API"
  value = var.create_custom_domain ? {
    for env in var.environments :
    env => "https://${var.environment_config[env].subdomain}.${var.domain_name}"
  } : {}
}

output "frontend_url" {
  description = "Frontend URL"
  value = var.create_custom_domain ? "https://www.${var.domain_name}" : "http://${aws_elastic_beanstalk_environment.frontend_env.cname}"
}

output "frontend_environment" {
  description = "Frontend Elastic Beanstalk environment name"
  value = aws_elastic_beanstalk_environment.frontend_env.name
}

output "frontend_pipeline" {
  description = "Frontend CodePipeline name"
  value = aws_codepipeline.frontend_pipeline.name
}

output "frontend_application" {
  description = "Frontend Elastic Beanstalk application name"
  value = aws_elastic_beanstalk_application.frontend_app.name
}

# GitHub integration uses OAuth token stored in AWS Secrets Manager
# No CodeStar connection outputs needed

output "pipeline_names" {
  description = "CodePipeline names for manual triggering"
  value = {
    for env in var.environments :
    env => aws_codepipeline.pipeline[env].name
  }
}

output "s3_bucket" {
  description = "S3 bucket for CI/CD artifacts"
  value       = aws_s3_bucket.artifacts.bucket
}

output "wildcard_certificate_arn" {
  description = "Wildcard SSL certificate ARN for all environments"
  value = var.create_custom_domain ? aws_acm_certificate.wildcard_cert[0].arn : null
}

output "deployment_summary" {
  description = "Quick deployment reference"
  value = {
    s3_bucket = aws_s3_bucket.artifacts.bucket
    pipelines = {
      for env in var.environments :
      env => aws_codepipeline.pipeline[env].name
    }
    urls = var.create_custom_domain ? {
      for env in var.environments :
      env => "https://${var.environment_config[env].subdomain}.${var.domain_name}"
    } : {
      for env in var.environments :
      env => "http://${aws_elastic_beanstalk_environment.app_env[env].cname}"
    }
    notifications = {
      sns_topic = aws_sns_topic.build_notifications.arn
      email     = var.notification_email
    }
  }
}

# Cost Optimization Information
output "cost_optimization_summary" {
  description = "Summary of cost optimization features enabled"
  value = {
    spot_instances_enabled = var.enable_spot_instances
    spot_max_price        = var.spot_max_price
    lifecycle_policy      = var.enable_lifecycle_policy
    auto_scaling_limits   = var.auto_scaling_max_size
    instance_type         = var.instance_type
    estimated_monthly_cost = {
      dev_environment   = var.enable_spot_instances ? "$5-15" : "$15-25"
      stage_environment = var.enable_spot_instances ? "$5-15" : "$15-25"
      prod_environment  = "$25-75"
      frontend         = "$15-25"
      s3_storage       = "$1-5"
      total_estimated  = var.enable_spot_instances ? "$50-135" : "$75-155"
    }
    cost_savings = {
      spot_instances = var.enable_spot_instances ? "60-90% on dev/stage" : "Not enabled"
      s3_lifecycle   = var.enable_lifecycle_policy ? "30-50% on storage" : "Not enabled"
      auto_scaling   = "Pay only for what you use"
    }
  }
}