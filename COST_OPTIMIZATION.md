# 💰 Complete Cost Optimization Guide

## 🎯 **Overview**

This guide covers all available cost optimization options for the Portfolio Infrastructure. You can now control **every component** independently for maximum cost savings.

## 🔧 **All Available Cost Control Flags**

### **Environment Control**
```hcl
enabled_environments = ["prod"]          # Choose which environments to create
enable_frontend = true                   # Enable/disable frontend
```

### **Storage Control**
```hcl
enable_s3_storage = true                 # Enable/disable S3 storage
max_build_versions = 3                   # Keep only latest N builds
s3_cleanup_days = 7                     # Delete artifacts after N days
enable_lifecycle_policy = true          # Enable automatic S3 cleanup
```

### **CI/CD Control**
```hcl
enable_codebuild = true                  # Enable/disable build processes
enable_codepipeline = true               # Enable/disable deployment pipelines
```

### **Monitoring & Notifications**
```hcl
enable_notifications = false            # Enable/disable SNS notifications
enable_enhanced_monitoring = false      # Enable/disable detailed metrics
```

### **Domain & Security**
```hcl
enable_custom_domain = false            # Enable/disable custom domain/SSL
enable_secrets_manager = false          # Enable/disable AWS Secrets Manager
```

### **Instance Optimization**
```hcl
enable_spot_instances = true            # Use spot instances for dev/stage
spot_max_price = "0.05"                 # Maximum spot instance price
```

## 💸 **Cost Breakdown by Component**

| **Component** | **Monthly Cost** | **Flag to Disable** | **Savings** |
|---------------|------------------|---------------------|-------------|
| **Elastic Beanstalk (Prod)** | $25-75 | `enabled_environments = []` | **$25-75** |
| **Frontend** | $15-25 | `enable_frontend = false` | **$15-25** |
| **Dev Environment** | $5-25 | Remove "dev" from `enabled_environments` | **$5-25** |
| **Stage Environment** | $5-25 | Remove "stage" from `enabled_environments` | **$5-25** |
| **CodeBuild** | $5-10 | `enable_codebuild = false` | **$5-10** |
| **S3 Storage** | $1-5 | `enable_s3_storage = false` | **$1-5** |
| **Enhanced Monitoring** | $2-5 | `enable_enhanced_monitoring = false` | **$2-5** |
| **SNS Notifications** | $1-2 | `enable_notifications = false` | **$1-2** |
| **CodePipeline** | $1/pipeline | `enable_codepipeline = false` | **$1-4** |
| **Custom Domain/SSL** | $0.50 | `enable_custom_domain = false` | **$0.50** |
| **Secrets Manager** | $0.40 | `enable_secrets_manager = false` | **$0.40** |

## 🎚️ **Cost Optimization Levels**

### **Level 1: Maximum Savings ($0/month)**
```hcl
# Disable everything
enabled_environments = []
enable_frontend = false
enable_s3_storage = false
enable_codebuild = false
enable_codepipeline = false
enable_notifications = false
enable_custom_domain = false
enable_secrets_manager = false
enable_enhanced_monitoring = false
```
**Use Case**: Project on hold, no active development

### **Level 2: Minimal Backend ($20-70/month)**
```hcl
# Production backend only, no extras
enabled_environments = ["prod"]
enable_frontend = false
enable_s3_storage = true
enable_codebuild = true
enable_codepipeline = true
enable_notifications = false
enable_custom_domain = false
enable_secrets_manager = false
enable_enhanced_monitoring = false
max_build_versions = 3
```
**Use Case**: Simple API backend only

### **Level 3: Standard Setup ($35-95/month)**
```hcl
# Production backend + frontend, optimized
enabled_environments = ["prod"]
enable_frontend = true
enable_s3_storage = true
enable_codebuild = true
enable_codepipeline = true
enable_notifications = false
enable_custom_domain = false
enable_secrets_manager = false
enable_enhanced_monitoring = false
max_build_versions = 3
```
**Use Case**: Full portfolio application, cost-optimized

### **Level 4: Development Mode ($45-130/month)**
```hcl
# All environments with spot instances
enabled_environments = ["dev", "stage", "prod"]
enable_frontend = true
enable_s3_storage = true
enable_codebuild = true
enable_codepipeline = true
enable_notifications = false
enable_custom_domain = false
enable_secrets_manager = false
enable_enhanced_monitoring = false
enable_spot_instances = true
max_build_versions = 3
```
**Use Case**: Active development with all environments

### **Level 5: Full Production ($60-150/month)**
```hcl
# Everything enabled for production use
enabled_environments = ["dev", "stage", "prod"]
enable_frontend = true
enable_s3_storage = true
enable_codebuild = true
enable_codepipeline = true
enable_notifications = true
enable_custom_domain = true
enable_secrets_manager = true
enable_enhanced_monitoring = true
enable_spot_instances = true
max_build_versions = 5
```
**Use Case**: Production-ready with full monitoring and features

## 🚀 **Quick Commands**

### **Apply Cost Level**
```bash
# Level 1: Maximum Savings
terraform apply \
  -var='enabled_environments=[]' \
  -var='enable_frontend=false' \
  -var='enable_s3_storage=false' \
  -var='enable_codebuild=false' \
  -var='enable_codepipeline=false'

# Level 2: Minimal Backend
terraform apply \
  -var='enabled_environments=["prod"]' \
  -var='enable_frontend=false' \
  -var='enable_custom_domain=false' \
  -var='enable_notifications=false'

# Level 3: Standard Setup
terraform apply -var-file="cost-control.tfvars"

# Level 4: Development Mode
terraform apply \
  -var='enabled_environments=["dev","stage","prod"]' \
  -var='enable_spot_instances=true'
```

### **Toggle Individual Components**
```bash
# Disable notifications
terraform apply -var='enable_notifications=false'

# Disable custom domain
terraform apply -var='enable_custom_domain=false'

# Disable CodeBuild (manual deployments only)
terraform apply -var='enable_codebuild=false'

# Enable only production
terraform apply -var='enabled_environments=["prod"]'
```

## 📊 **Cost Monitoring**

### **View Current Configuration**
```bash
terraform output cost_optimization_summary
```

### **Estimate Costs**
```bash
# Check what will change
terraform plan -var-file="cost-control.tfvars"

# Apply and see cost summary
terraform apply -var-file="cost-control.tfvars"
terraform output cost_optimization_summary
```

## 🔄 **Scaling Strategies**

### **Start Small, Scale Up**
1. **Begin**: Level 2 (Minimal Backend) - $20-70/month
2. **Add Frontend**: Level 3 (Standard Setup) - $35-95/month  
3. **Add Development**: Level 4 (Development Mode) - $45-130/month
4. **Add Monitoring**: Level 5 (Full Production) - $60-150/month

### **Scale Down for Savings**
1. **Remove Dev/Stage**: Save $10-50/month
2. **Disable Notifications**: Save $1-2/month
3. **Disable Custom Domain**: Save $0.50/month
4. **Disable Enhanced Monitoring**: Save $2-5/month
5. **Disable CodeBuild**: Save $5-10/month (manual deployments)

## 🎯 **Best Practices**

### **For Development**
- Use **Level 4** during active development
- Scale down to **Level 2** when not actively developing
- Use spot instances for dev/stage environments

### **For Production**
- Start with **Level 3** for cost-effective production
- Upgrade to **Level 5** when you need full monitoring
- Keep production on on-demand instances for reliability

### **For Cost Optimization**
- Review costs monthly and adjust flags as needed
- Use `terraform plan` to preview cost changes
- Monitor AWS billing dashboard for actual costs
- Consider Reserved Instances for predictable workloads

## 🚨 **Important Notes**

### **Dependencies**
- `enable_codepipeline` requires `enable_codebuild` and `enable_s3_storage`
- `enable_custom_domain` requires a valid domain in Route53
- `enable_secrets_manager` is only needed if using GitHub token authentication

### **Data Loss Warnings**
- `enable_s3_storage = false` **deletes all build artifacts**
- `enabled_environments = []` **deletes all application environments**
- Always backup important data before major changes

### **GitHub Actions Compatibility**
- GitHub Actions can still push builds with any configuration
- Automatic deployments require `enable_codepipeline = true`
- Manual deployments work even with CodePipeline disabled

---

**💡 Pro Tip**: Use the cost control configuration file (`cost-control.tfvars`) to easily switch between optimization levels. Start conservative and scale up as needed!
