#!/bin/bash

# Frontend Version Deployment Script
# Usage: ./deploy-frontend-version.sh [version_name]

set -e

# Configuration
S3_BUCKET="portfolio-app-cicd-artifacts"
S3_PREFIX="frontend-versions"
PIPELINE_NAME="portfolio-app-frontend-deploy-pipeline"
EB_APPLICATION="portfolio-app-frontend"
EB_ENVIRONMENT="portfolio-app-frontend-env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_usage() {
    echo -e "${BLUE}Frontend Version Deployment Script${NC}"
    echo ""
    echo "Usage: $0 [version_name]"
    echo ""
    echo "Examples:"
    echo "  $0                                    # List available versions and select"
    echo "  $0 v20250620-143022-abc12345-build123 # Deploy specific version"
    echo ""
    echo "This script deploys a pre-built frontend version from S3 to Elastic Beanstalk"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi
}

list_available_versions() {
    print_info "Available frontend versions in S3:"
    echo ""
    
    local versions
    versions=$(aws s3 ls s3://$S3_BUCKET/$S3_PREFIX/ --recursive | grep "\.zip$" | sort -k1,2 -r)
    
    if [[ -z "$versions" ]]; then
        print_error "No versions found in S3 bucket s3://$S3_BUCKET/$S3_PREFIX/"
        exit 1
    fi
    
    local count=1
    echo "$versions" | while read -r line; do
        local date_time=$(echo "$line" | awk '{print $1, $2}')
        local size=$(echo "$line" | awk '{print $3}')
        local key=$(echo "$line" | awk '{print $4}')
        local version=$(basename "$key" .zip)
        
        echo -e "${BLUE}[$count]${NC} $version"
        echo "    Date: $date_time"
        echo "    Size: $size bytes"
        echo ""
        
        count=$((count + 1))
    done
}

get_version_info() {
    local version=$1
    local info_key="$S3_PREFIX/${version}-info.json"
    
    if aws s3 ls s3://$S3_BUCKET/$info_key &> /dev/null; then
        print_info "Version information:"
        aws s3 cp s3://$S3_BUCKET/$info_key - | jq -r '
            "  Version: " + .version + "\n" +
            "  Build Date: " + .buildDate + "\n" +
            "  Git Commit: " + .gitCommit + "\n" +
            "  Build Number: " + .buildNumber + "\n" +
            "  Branch: " + (.branch // "unknown") + "\n" +
            "  Backend Env: " + .backendEnv + "\n" +
            "  API URL: " + .apiUrl
        ' 2>/dev/null || echo "  (Version info not available)"
    fi
}

select_version_interactive() {
    local versions
    versions=$(aws s3 ls s3://$S3_BUCKET/$S3_PREFIX/ --recursive | grep "\.zip$" | sort -k1,2 -r | head -20)
    
    if [[ -z "$versions" ]]; then
        print_error "No versions found in S3"
        exit 1
    fi
    
    echo ""
    print_info "Recent frontend versions:"
    echo ""
    
    local version_array=()
    local count=1
    
    while read -r line; do
        local key=$(echo "$line" | awk '{print $4}')
        local version=$(basename "$key" .zip)
        local date_time=$(echo "$line" | awk '{print $1, $2}')
        
        version_array+=("$version")
        echo -e "${BLUE}[$count]${NC} $version ($date_time)"
        
        count=$((count + 1))
    done <<< "$versions"
    
    echo ""
    echo "Enter version number (1-$((count-1))) or 'q' to quit:"
    read -r selection
    
    if [[ "$selection" == "q" ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    
    if [[ "$selection" =~ ^[0-9]+$ ]] && [[ "$selection" -ge 1 ]] && [[ "$selection" -lt "$count" ]]; then
        echo "${version_array[$((selection-1))]}"
    else
        print_error "Invalid selection"
        exit 1
    fi
}

check_version_exists() {
    local version=$1
    local s3_key="$S3_PREFIX/${version}.zip"
    
    if ! aws s3 ls s3://$S3_BUCKET/$s3_key &> /dev/null; then
        print_error "Version '$version' not found in S3"
        print_info "Available versions:"
        list_available_versions
        exit 1
    fi
}

deploy_version() {
    local version=$1
    local s3_key="$S3_PREFIX/${version}.zip"

    print_info "Deploying version: $version"
    print_info "S3 Location: s3://$S3_BUCKET/$s3_key"
    print_info "Pipeline: $PIPELINE_NAME"
    print_info "Application: $EB_APPLICATION"
    print_info "Environment: $EB_ENVIRONMENT"

    # Update pipeline source configuration to point to the selected version
    print_info "Updating pipeline source configuration..."

    # Get current pipeline configuration
    local pipeline_config
    pipeline_config=$(aws codepipeline get-pipeline --name "$PIPELINE_NAME" --query 'pipeline' --output json)

    if [[ $? -ne 0 ]]; then
        print_error "Failed to get pipeline configuration"
        exit 1
    fi

    # Update the S3ObjectKey in the source action
    local updated_config
    updated_config=$(echo "$pipeline_config" | jq --arg s3_key "$s3_key" '
        .stages[0].actions[0].configuration.S3ObjectKey = $s3_key
    ')

    # Update the pipeline
    aws codepipeline update-pipeline --pipeline "$updated_config" > /dev/null

    if [[ $? -eq 0 ]]; then
        print_success "Pipeline configuration updated successfully"
    else
        print_error "Failed to update pipeline configuration"
        exit 1
    fi

    # Start pipeline execution
    print_info "Starting pipeline execution..."

    local execution_id
    execution_id=$(aws codepipeline start-pipeline-execution \
        --name "$PIPELINE_NAME" \
        --query 'pipelineExecutionId' \
        --output text)

    if [[ $? -eq 0 && -n "$execution_id" ]]; then
        print_success "Pipeline execution started successfully!"
        print_info "Execution ID: $execution_id"
        print_info "Monitor deployment progress at:"
        print_info "https://console.aws.amazon.com/codesuite/codepipeline/pipelines/$PIPELINE_NAME/view"
        print_info "Elastic Beanstalk Console:"
        print_info "https://console.aws.amazon.com/elasticbeanstalk/home#/environment/dashboard?environmentId=$EB_ENVIRONMENT"
    else
        print_error "Failed to start pipeline execution"
        exit 1
    fi
}

# Main script
main() {
    local version_name=$1
    
    # Show usage if help requested
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        print_usage
        exit 0
    fi
    
    print_info "Frontend Version Deployment Tool"
    echo ""
    
    # Checks
    print_info "Performing pre-deployment checks..."
    check_aws_cli
    
    # If no version specified, show interactive selection
    if [[ -z "$version_name" ]]; then
        version_name=$(select_version_interactive)
    fi
    
    # Validate version exists
    check_version_exists "$version_name"
    
    # Show version info
    get_version_info "$version_name"
    
    # Confirm deployment
    echo ""
    print_warning "Ready to deploy version: $version_name"
    print_warning "This will update the production frontend environment"
    echo ""
    echo "Continue? (y/N)"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    
    # Deploy
    deploy_version "$version_name"
}

# Run main function
main "$@"
