version: 0.2

env:
  variables:
    NODE_VERSION: "22"
    REACT_APP_ENVIRONMENT: "production"
    BACKEND_ENV: "prod"

phases:
  install:
    runtime-versions:
      nodejs: $NODE_VERSION
    commands:
      - echo "=== INSTALL PHASE ==="
      - echo "Build ID: $CODEBUILD_BUILD_ID"
      - echo "Build Number: $CODEBUILD_BUILD_NUMBER"
      - echo "Source Version: $CODEBUILD_RESOLVED_SOURCE_VERSION"
      - echo "Installing npm dependencies..."
      - npm install
      - echo "Dependencies installed successfully"

  pre_build:
    commands:
      - echo "=== PRE_BUILD PHASE ==="
      - echo "Setting up environment for $BACKEND_ENV"
      - |
        if [ "$BACKEND_ENV" = "dev" ]; then
          API_URL="https://dev-api.manojpatidar.in/api/v1"
          echo "Using DEV API: $API_URL"
        elif [ "$BACKEND_ENV" = "stage" ]; then
          API_URL="https://stage-api.manojpatidar.in/api/v1"
          echo "Using STAGE API: $API_URL"
        else
          API_URL="https://api.manojpatidar.in/api/v1"
          echo "Using PRODUCTION API: $API_URL"
        fi
      - echo "Creating environment configuration..."
      - |
        cat > .env.production << EOF
        REACT_APP_API_URL=$API_URL
        REACT_APP_DOMAIN=manojpatidar.in
        REACT_APP_ENVIRONMENT=production
        REACT_APP_BACKEND_ENV=$BACKEND_ENV
        REACT_APP_NAME=Manoj Patidar Portfolio
        REACT_APP_VERSION=1.0.0
        REACT_APP_AUTHOR=Manoj Patidar
        REACT_APP_EMAIL=<EMAIL>
        REACT_APP_GITHUB=https://github.com/learning-orgs
        REACT_APP_LINKEDIN=https://linkedin.com/in/manojpatidar
        REACT_APP_ENABLE_AI_FEATURES=true
        REACT_APP_ENABLE_VOICE_ASSISTANT=true
        REACT_APP_ENABLE_3D_PORTFOLIO=true
        REACT_APP_ENABLE_CHATBOT=true
        REACT_APP_ENABLE_ANALYTICS=true
        GENERATE_SOURCEMAP=false
        REACT_APP_DEBUG_MODE=false
        EOF
      - echo "Environment configuration created"
      - echo "Running tests..."
      - npm test -- --coverage --watchAll=false --passWithNoTests || echo "Tests failed but continuing"
      - echo "Pre-build phase completed"

  build:
    commands:
      - echo "=== BUILD PHASE ==="
      - echo "Building React application..."
      - npm run build
      - echo "Build completed successfully"
      - echo "Build directory contents:"
      - ls -la build/

  post_build:
    commands:
      - echo "=== POST_BUILD PHASE ==="
      - echo "Preparing deployment package for CodePipeline..."
      - echo "Current directory contents:"
      - ls -la
      - echo "Backing up original package.json..."
      - cp package.json package.json.backup
      - echo "Creating production package.json for Elastic Beanstalk..."
      - |
        cat > package.json << EOF
        {
          "name": "portfolio-frontend",
          "version": "1.0.0",
          "description": "Manoj Patidar Portfolio Frontend",
          "main": "server.js",
          "scripts": {
            "start": "node server.js"
          },
          "dependencies": {
            "express": "^4.18.2",
            "compression": "^1.7.4",
            "helmet": "^7.0.0",
            "cors": "^2.8.5"
          },
          "engines": {
            "node": "22.x",
            "npm": "10.x"
          }
        }
        EOF
      - echo "Production package.json created"
      - echo "Creating Express server for production..."
      - |
        cat > server.js << 'EOF'
        const express = require('express');
        const path = require('path');
        const compression = require('compression');
        const helmet = require('helmet');
        const cors = require('cors');

        const app = express();
        const port = process.env.PORT || 3000;

        app.use(helmet({ contentSecurityPolicy: false }));
        app.use(compression());
        app.use(cors());
        app.use(express.static(path.join(__dirname, 'build')));

        app.get('/health', (req, res) => {
          res.status(200).json({
            status: 'healthy',
            environment: process.env.REACT_APP_ENVIRONMENT || 'unknown',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
          });
        });

        app.get('*', (req, res) => {
          res.sendFile(path.join(__dirname, 'build', 'index.html'));
        });

        app.listen(port, () => {
          console.log('Frontend server running on port ' + port);
          console.log('Environment: ' + (process.env.REACT_APP_ENVIRONMENT || 'unknown'));
        });
        EOF
      - echo "Express server created"
      - echo "Creating Procfile for Elastic Beanstalk..."
      - echo "web: node server.js" > Procfile
      - echo "Procfile created"
      - echo "Installing production dependencies..."
      - npm install --production
      - echo "Production dependencies installed"
      - echo "=== FINAL VERIFICATION ==="
      - echo "Verifying all required files exist:"
      - ls -la package.json server.js Procfile build/ node_modules/
      - echo "Checking file contents:"
      - echo "--- package.json ---"
      - head -10 package.json
      - echo "--- server.js ---"
      - head -5 server.js
      - echo "--- Procfile ---"
      - cat Procfile
      - echo "--- build directory ---"
      - ls -la build/ | head -10
      - echo "=== DEPLOYMENT READY ==="
      - echo "All files prepared for CodePipeline deployment"
      - echo "CodePipeline will handle the ZIP creation and deployment"

artifacts:
  files:
    - package.json
    - server.js
    - Procfile
    - build/**/*
    - node_modules/**/*
  name: frontend-build-$CODEBUILD_BUILD_NUMBER

cache:
  paths:
    - node_modules/**/*
