# AWS Full-Stack CI/CD Pipeline
# Infrastructure for both Spring Boot backend and React frontend deployment
#
# This Terraform configuration creates :
# - Existing Spring Boot backend infrastructure (unchanged)
# - New React frontend infrastructure with GitHub integration
# - Single production deployment for cost optimization
# - Configurable backend connectivity (dev/stage/prod)
# - AWS Secrets Manager integration for secure credential storage

# Terraform configuration block
# Specifies required Terraform version and AWS provider
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure AWS Provider
# Sets up the AWS provider with default tags for all resources
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project   = var.project_name
      ManagedBy = "Terraform"
      Purpose   = "Multi-Environment-Pipeline"
    }
  }
}

# Data sources for current AWS account and region information
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# AWS Secrets Manager - GitHub Token
# Stores GitHub personal access token securely for CodePipeline integration
# This secret must be manually populated with your GitHub token after creation
resource "aws_secretsmanager_secret" "github_token" {
  name        = "${var.project_name}/github/token"
  description = "GitHub personal access token for CodePipeline integration"

  # Recovery window for deleted secrets (7-30 days)
  recovery_window_in_days = 7

  tags = {
    Name        = "${var.project_name}-github-token"
    Environment = "all"
    Type        = "Credential"
    Purpose     = "GitHub Integration"
  }
}

# GitHub Token Secret Version (Placeholder)
# The actual token should be set manually after Terraform deployment
# Use: aws secretsmanager put-secret-value --secret-id portfolio/github/token --secret-string "your-github-token"
resource "aws_secretsmanager_secret_version" "github_token" {
  secret_id = aws_secretsmanager_secret.github_token.id

  # Placeholder value - MUST be replaced with actual GitHub token
  secret_string = jsonencode({
    token = "PLACEHOLDER_REPLACE_WITH_ACTUAL_GITHUB_TOKEN"
  })

  # Ignore changes to prevent Terraform from overwriting manually set values
  lifecycle {
    ignore_changes = [secret_string]
  }
}

# Data source to retrieve GitHub token from Secrets Manager
# This allows CodePipeline to access the token securely at runtime
data "aws_secretsmanager_secret_version" "github_token" {
  secret_id = aws_secretsmanager_secret.github_token.id
  depends_on = [aws_secretsmanager_secret_version.github_token]
}

# CodeStar Connection is created manually in AWS Console
# This avoids Terraform managing the GitHub authorization process
# Connection ARN is provided via variable



# SNS Topic for Build Notifications (Optional)
resource "aws_sns_topic" "build_notifications" {
  count = var.enable_notifications ? 1 : 0

  name = "${var.project_name}-build-notifications"

  tags = {
    Name    = "${var.project_name}-build-notifications"
    Project = var.project_name
  }
}

# SNS Topic Subscription for Email Notifications (Optional)
resource "aws_sns_topic_subscription" "email_notification" {
  count = var.enable_notifications ? 1 : 0

  topic_arn = aws_sns_topic.build_notifications[0].arn
  protocol  = "email"
  endpoint  = var.notification_email
}

# S3 Bucket for CodePipeline Artifacts (Conditional)
resource "aws_s3_bucket" "artifacts" {
  count = var.enable_s3_storage ? 1 : 0

  bucket = "${var.project_name}-cicd-artifacts"
}

resource "aws_s3_bucket_versioning" "artifacts" {
  count = var.enable_s3_storage ? 1 : 0

  bucket = aws_s3_bucket.artifacts[0].id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Lifecycle Configuration for Cost Optimization (Conditional)
resource "aws_s3_bucket_lifecycle_configuration" "artifacts_lifecycle" {
  count = var.enable_s3_storage && var.enable_lifecycle_policy ? 1 : 0

  bucket = aws_s3_bucket.artifacts[0].id

  # Keep only latest 3 build versions for each environment
  rule {
    id     = "keep_latest_builds"
    status = "Enabled"

    filter {
      prefix = ""  # Apply to all objects
    }

    # Keep only the latest 3 versions of each object
    noncurrent_version_expiration {
      newer_noncurrent_versions = var.max_build_versions
      noncurrent_days          = 1  # Delete immediately after exceeding count
    }
  }

  # Clean up incomplete multipart uploads
  rule {
    id     = "cleanup_incomplete_uploads"
    status = "Enabled"

    filter {
      prefix = ""
    }

    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }

  # Clean up old frontend versions specifically
  rule {
    id     = "frontend_versions_cleanup"
    status = "Enabled"

    filter {
      prefix = "frontend-versions/"
    }

    # Keep only latest 3 frontend versions
    noncurrent_version_expiration {
      newer_noncurrent_versions = var.max_build_versions
      noncurrent_days          = 1
    }
  }

  # Clean up environment-specific builds
  rule {
    id     = "env_builds_cleanup"
    status = "Enabled"

    filter {
      prefix = "dev/"
    }

    noncurrent_version_expiration {
      newer_noncurrent_versions = var.max_build_versions
      noncurrent_days          = 1
    }
  }

  rule {
    id     = "stage_builds_cleanup"
    status = "Enabled"

    filter {
      prefix = "stage/"
    }

    noncurrent_version_expiration {
      newer_noncurrent_versions = var.max_build_versions
      noncurrent_days          = 1
    }
  }

  rule {
    id     = "prod_builds_cleanup"
    status = "Enabled"

    filter {
      prefix = "prod/"
    }

    noncurrent_version_expiration {
      newer_noncurrent_versions = var.max_build_versions
      noncurrent_days          = 1
    }
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "artifacts" {
  count = var.enable_s3_storage ? 1 : 0

  bucket = aws_s3_bucket.artifacts[0].id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 bucket public access block (Conditional)
resource "aws_s3_bucket_public_access_block" "artifacts" {
  count = var.enable_s3_storage ? 1 : 0

  bucket = aws_s3_bucket.artifacts[0].id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

# Removed random_string - using predictable bucket name instead

# IAM Role for CodePipeline
resource "aws_iam_role" "codepipeline_role" {
  name = "${var.project_name}-codepipeline-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codepipeline.amazonaws.com"
        }
      }
    ]
  })
}

# CodePipeline IAM Policy
# Grants necessary permissions for CodePipeline to access CodeBuild, IAM, and Secrets Manager
resource "aws_iam_role_policy" "codepipeline_policy" {
  name = "${var.project_name}-codepipeline-policy"
  role = aws_iam_role.codepipeline_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "codebuild:BatchGetBuilds",
          "codebuild:StartBuild"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iam:PassRole"
        ]
        Resource = "*"
      },
      {
        # Allow CodePipeline to access GitHub token from Secrets Manager
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = aws_secretsmanager_secret.github_token.arn
      },
      {
        # Allow CodePipeline to use manually created CodeStar connection for GitHub
        Effect = "Allow"
        Action = [
          "codestar-connections:UseConnection"
        ]
        Resource = var.github_connection_arn != "" ? var.github_connection_arn : "*"
      }
    ]
  })
}

# Attach AWS managed policies for comprehensive permissions
resource "aws_iam_role_policy_attachment" "codepipeline_full_access" {
  role       = aws_iam_role.codepipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess"
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_full_access" {
  role       = aws_iam_role.codepipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess-AWSElasticBeanstalk"
}

resource "aws_iam_role_policy_attachment" "s3_full_access" {
  role       = aws_iam_role.codepipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

# IAM Role for CodeBuild
resource "aws_iam_role" "codebuild_role" {
  name = "${var.project_name}-codebuild-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
      }
    ]
  })
}

# IAM Role for Elastic Beanstalk EC2 instances
resource "aws_iam_role" "eb_ec2_role" {
  name = "${var.project_name}-eb-ec2-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

# Attach AWS managed policies to the EC2 role
resource "aws_iam_role_policy_attachment" "eb_web_tier" {
  role       = aws_iam_role.eb_ec2_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSElasticBeanstalkWebTier"
}

resource "aws_iam_role_policy_attachment" "eb_worker_tier" {
  role       = aws_iam_role.eb_ec2_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSElasticBeanstalkWorkerTier"
}

resource "aws_iam_role_policy_attachment" "eb_multicontainer_docker" {
  role       = aws_iam_role.eb_ec2_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSElasticBeanstalkMulticontainerDocker"
}

# Custom policy for Parameter Store access
resource "aws_iam_role_policy" "eb_parameter_store_policy" {
  name = "${var.project_name}-eb-parameter-store-policy"
  role = aws_iam_role.eb_ec2_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath"
        ]
        Resource = [
          "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/portfolio/*"
        ]
      }
    ]
  })
}

# Create instance profile
resource "aws_iam_instance_profile" "eb_ec2_profile" {
  name = "${var.project_name}-eb-ec2-profile"
  role = aws_iam_role.eb_ec2_role.name
}

# IAM Role for Elastic Beanstalk Service
resource "aws_iam_role" "eb_service_role" {
  name = "${var.project_name}-eb-service-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "elasticbeanstalk.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eb_service" {
  role       = aws_iam_role.eb_service_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSElasticBeanstalkService"
}

resource "aws_iam_role_policy_attachment" "eb_health" {
  role       = aws_iam_role.eb_service_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSElasticBeanstalkEnhancedHealth"
}

# Using S3 source instead of CodeStar connections

resource "aws_iam_role_policy" "codebuild_policy" {
  name = "${var.project_name}-codebuild-policy"
  role = aws_iam_role.codebuild_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:GetObjectAcl",
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:ListBucket",
          "s3:DeleteObject",
          "s3:PutObjectVersionAcl"
        ]
        Resource = var.enable_s3_storage ? [
          aws_s3_bucket.artifacts[0].arn,
          "${aws_s3_bucket.artifacts[0].arn}/*"
        ] : []
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:DeleteObject",
          "s3:ListBucket",
          "s3:GetBucketLocation",
          "s3:GetBucketVersioning"
        ]
        Resource = flatten([
          for env in var.environments : [
            "arn:aws:s3:::${var.project_name}-frontend-${env}",
            "arn:aws:s3:::${var.project_name}-frontend-${env}/*"
          ]
        ])
      },
      {
        Effect = "Allow"
        Action = [
          "cloudfront:CreateInvalidation",
          "cloudfront:GetInvalidation",
          "cloudfront:ListInvalidations"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "elasticbeanstalk:CreateApplicationVersion",
          "elasticbeanstalk:UpdateEnvironment",
          "elasticbeanstalk:DescribeApplicationVersions",
          "elasticbeanstalk:DescribeEnvironments",
          "elasticbeanstalk:DescribeApplications"
        ]
        Resource = "*"
      }
    ]
  })
}

# CodeBuild Projects for enabled environments only (conditional)
resource "aws_codebuild_project" "build" {
  for_each = var.enable_codebuild ? toset(var.enabled_environments) : []

  name          = "${var.project_name}-${each.key}-build"
  description   = "Build project for ${var.project_name} ${each.key} environment"
  service_role  = aws_iam_role.codebuild_role.arn

  artifacts {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"  # Cost optimized
    image                      = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                       = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"

    environment_variable {
      name  = "ENVIRONMENT"
      value = each.key
    }

    environment_variable {
      name  = "ARTIFACT_BUCKET"
      value = var.enable_s3_storage ? aws_s3_bucket.artifacts[0].bucket : "disabled"
    }

    # Enable detailed logging via environment variables
    environment_variable {
      name  = "BUILD_VERBOSE"
      value = "true"
    }

    environment_variable {
      name  = "AWS_DEFAULT_OUTPUT"
      value = "text"
    }
  }

  source {
    type = "CODEPIPELINE"
    buildspec = "buildspec.yml"
  }

  tags = {
    Environment = each.key
  }
}

# CodeBuild Project for Frontend (Conditional)
resource "aws_codebuild_project" "frontend_build" {
  count = var.enable_frontend ? 1 : 0

  name          = "${var.project_name}-frontend-build"
  description   = "Build project for ${var.project_name} frontend with GitHub integration"
  service_role  = aws_iam_role.codebuild_role.arn

  artifacts {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"  # Cost optimized
    image                      = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                       = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"

    # Default environment variables (can be overridden in pipeline)
    environment_variable {
      name  = "REACT_APP_ENVIRONMENT"
      value = "production"
    }

    environment_variable {
      name  = "REACT_APP_API_URL"
      value = var.create_custom_domain ? "https://api.${var.domain_name}/api/v1" : "http://localhost:8080/api/v1"
    }

    environment_variable {
      name  = "REACT_APP_DOMAIN"
      value = var.domain_name
    }

    environment_variable {
      name  = "ARTIFACT_BUCKET"
      value = var.enable_s3_storage ? aws_s3_bucket.artifacts[0].bucket : "disabled"
    }

    # Branch selection (can be overridden)
    environment_variable {
      name  = "GITHUB_BRANCH"
      value = "main"
    }

    # Backend API selection (can be overridden)
    environment_variable {
      name  = "BACKEND_ENV"
      value = var.backend_environment
    }

    # Enable detailed logging via environment variables
    environment_variable {
      name  = "BUILD_VERBOSE"
      value = "true"
    }

    environment_variable {
      name  = "NPM_CONFIG_LOGLEVEL"
      value = "info"
    }

    environment_variable {
      name  = "AWS_DEFAULT_OUTPUT"
      value = "text"
    }
  }

  source {
    type = "CODEPIPELINE"
    buildspec = "buildspec.yml"
  }

  tags = {
    Environment = "production"
    Type        = "Frontend"
  }
}

# Elastic Beanstalk Applications for enabled environments only
resource "aws_elastic_beanstalk_application" "app" {
  for_each = toset(var.enabled_environments)

  name        = "${var.project_name}-${each.key}"
  description = "Java Spring Boot application for ${each.key} environment"

  tags = {
    Environment = each.key
  }
}

# Elastic Beanstalk Environments for enabled environments only
resource "aws_elastic_beanstalk_environment" "app_env" {
  for_each = toset(var.enabled_environments)

  name                = "${var.project_name}-${each.key}-env"
  application         = aws_elastic_beanstalk_application.app[each.key].name
  solution_stack_name = "64bit Amazon Linux 2 v3.8.2 running Corretto 17"

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "EnvironmentType"
    value     = "LoadBalanced"  # Required for HTTPS with custom domain
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "InstanceType"
    value     = var.instance_type
  }

  # Use Spot Instances for non-production environments (60-90% cost savings)
  dynamic "setting" {
    for_each = each.key != "prod" ? [1] : []
    content {
      namespace = "aws:ec2:instances"
      name      = "EnableSpot"
      value     = "true"
    }
  }

  dynamic "setting" {
    for_each = each.key != "prod" ? [1] : []
    content {
      namespace = "aws:ec2:instances"
      name      = "SpotMaxPrice"
      value     = "0.05"  # Max $0.05/hour for t2.micro spot
    }
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.eb_ec2_profile.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "ServiceRole"
    value     = aws_iam_role.eb_service_role.arn
  }

  setting {
    namespace = "aws:elasticbeanstalk:healthreporting:system"
    name      = "SystemType"
    value     = "basic"  # Cost optimization
  }

  # Auto Scaling Configuration - Optimized for cost
  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = "1"
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = each.key == "prod" ? "3" : "1"  # Only prod can scale up
  }

  # Cost optimization: Scale down during off-hours for non-prod
  dynamic "setting" {
    for_each = each.key != "prod" ? [1] : []
    content {
      namespace = "aws:autoscaling:trigger"
      name      = "MeasureName"
      value     = "CPUUtilization"
    }
  }

  dynamic "setting" {
    for_each = each.key != "prod" ? [1] : []
    content {
      namespace = "aws:autoscaling:trigger"
      name      = "UpperThreshold"
      value     = "80"  # Scale up at 80% CPU
    }
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SPRING_PROFILES_ACTIVE"
    value     = each.key
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SERVER_PORT"
    value     = "5000"
  }

  # HTTPS Configuration (only if custom domain is enabled)
  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "ListenerProtocol"
      value     = "HTTPS"
    }
  }

  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "InstancePort"
      value     = "5000"
    }
  }

  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "InstanceProtocol"
      value     = "HTTP"
    }
  }

  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "SSLCertificateId"
      value     = aws_acm_certificate.wildcard_cert[0].arn
    }
  }

  tags = {
    Environment = each.key
    Project     = var.project_name
    CostCenter  = "Development"
    Owner       = "DevOps"
    Terraform   = "true"
    Type        = "Backend"
  }
}

# CodePipelines for enabled environments only (manually triggered, requires S3)
resource "aws_codepipeline" "pipeline" {
  for_each = var.enable_s3_storage ? toset(var.enabled_environments) : []

  name     = "${var.project_name}-${each.key}-pipeline"
  role_arn = aws_iam_role.codepipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.artifacts[0].bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        S3Bucket    = aws_s3_bucket.artifacts[0].bucket
        S3ObjectKey = "${each.key}/source.zip"
        PollForSourceChanges = false  # Manual trigger only
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "Build"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      input_artifacts  = ["source_output"]
      output_artifacts = ["build_output"]
      version          = "1"

      configuration = {
        ProjectName = aws_codebuild_project.build[each.key].name
      }
    }
  }

  stage {
    name = "Deploy"

    action {
      name            = "Deploy"
      category        = "Deploy"
      owner           = "AWS"
      provider        = "ElasticBeanstalk"
      input_artifacts = ["build_output"]
      version         = "1"

      configuration = {
        ApplicationName = aws_elastic_beanstalk_application.app[each.key].name
        EnvironmentName = aws_elastic_beanstalk_environment.app_env[each.key].name
      }
    }
  }

  tags = {
    Environment = each.key
  }
}

# Frontend Elastic Beanstalk Application (Conditional)
resource "aws_elastic_beanstalk_application" "frontend_app" {
  count = var.enable_frontend ? 1 : 0

  name        = "${var.project_name}-frontend"
  description = "React Node.js application for production"

  tags = {
    Environment = "production"
    Type        = "Frontend"
  }
}

# Frontend Elastic Beanstalk Environment (Conditional)
# Uses Amazon Linux 2023 with Node.js 18 for optimal performance and security
resource "aws_elastic_beanstalk_environment" "frontend_env" {
  count = var.enable_frontend ? 1 : 0

  name                = "${var.project_name}-frontend-env"
  application         = aws_elastic_beanstalk_application.frontend_app[0].name
  solution_stack_name = "64bit Amazon Linux 2023 v6.5.2 running Node.js 22"

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "EnvironmentType"
    value     = "LoadBalanced"  # Required for HTTPS with custom domain
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "InstanceType"
    value     = var.instance_type
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.eb_ec2_profile.name
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "ServiceRole"
    value     = aws_iam_role.eb_service_role.arn
  }

  setting {
    namespace = "aws:elasticbeanstalk:healthreporting:system"
    name      = "SystemType"
    value     = "basic"  # Cost optimization
  }

  # Auto Scaling Configuration - Cost optimized
  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = "1"
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = "2"  # Frontend can scale to 2 instances max
  }

  # CPU-based scaling
  setting {
    namespace = "aws:autoscaling:trigger"
    name      = "MeasureName"
    value     = "CPUUtilization"
  }

  setting {
    namespace = "aws:autoscaling:trigger"
    name      = "UpperThreshold"
    value     = "75"  # Scale up at 75% CPU
  }

  # Node.js specific settings
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NODE_ENV"
    value     = "production"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "REACT_APP_ENVIRONMENT"
    value     = "production"
  }

  # Configurable API URL - can be changed during deployment
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "REACT_APP_API_URL"
    value     = var.create_custom_domain ? "https://api.${var.domain_name}/api/v1" : "http://localhost:8080/api/v1"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "PORT"
    value     = "3000"
  }

  # HTTPS Configuration (only if custom domain is enabled)
  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "ListenerProtocol"
      value     = "HTTPS"
    }
  }

  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "InstancePort"
      value     = "3000"
    }
  }

  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "InstanceProtocol"
      value     = "HTTP"
    }
  }

  dynamic "setting" {
    for_each = var.create_custom_domain ? [1] : []
    content {
      namespace = "aws:elb:listener:443"
      name      = "SSLCertificateId"
      value     = aws_acm_certificate.wildcard_cert[0].arn
    }
  }

  tags = {
    Environment = "production"
    Type        = "Frontend"
  }
}

# Frontend Deployment Pipeline - Deploys Selected Versions from S3 (Conditional)
resource "aws_codepipeline" "frontend_pipeline" {
  count = var.enable_frontend && var.enable_s3_storage ? 1 : 0

  name     = "${var.project_name}-frontend-deploy-pipeline"
  role_arn = aws_iam_role.codepipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.artifacts[0].bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["frontend_deploy_output"]

      configuration = {
        S3Bucket             = aws_s3_bucket.artifacts[0].bucket
        S3ObjectKey          = "frontend-versions/PLACEHOLDER_VERSION.zip"
        PollForSourceChanges = "false"  # Manual trigger only
      }
    }
  }

  stage {
    name = "Deploy"

    action {
      name            = "Deploy"
      category        = "Deploy"
      owner           = "AWS"
      provider        = "ElasticBeanstalk"
      input_artifacts = ["frontend_deploy_output"]
      version         = "1"

      configuration = {
        ApplicationName = aws_elastic_beanstalk_application.frontend_app[0].name
        EnvironmentName = aws_elastic_beanstalk_environment.frontend_env[0].name
      }
    }
  }



  tags = {
    Environment = "production"
    Type        = "Frontend-Deploy"
  }
}



# CloudWatch Event Rule for Pipeline State Changes (enabled environments only, requires notifications)
resource "aws_cloudwatch_event_rule" "pipeline_state_change" {
  for_each = var.enable_notifications ? toset(var.enabled_environments) : []

  name        = "${var.project_name}-${each.key}-pipeline-state-change"
  description = "Capture pipeline state changes for ${each.key} environment"

  event_pattern = jsonencode({
    source      = ["aws.codepipeline"]
    detail-type = ["CodePipeline Pipeline Execution State Change"]
    detail = {
      pipeline = [aws_codepipeline.pipeline[each.key].name]
      state    = ["SUCCEEDED", "FAILED"]
    }
  })

  tags = {
    Name        = "${var.project_name}-${each.key}-pipeline-state-change"
    Environment = each.key
    Project     = var.project_name
  }
}

# CloudWatch Event Target for SNS (enabled environments only, requires notifications)
resource "aws_cloudwatch_event_target" "sns_target" {
  for_each = var.enable_notifications ? toset(var.enabled_environments) : []

  rule      = aws_cloudwatch_event_rule.pipeline_state_change[each.key].name
  target_id = "SendToSNS"
  arn       = aws_sns_topic.build_notifications[0].arn

  input_transformer {
    input_paths = {
      pipeline = "$.detail.pipeline"
      state    = "$.detail.state"
      time     = "$.time"
      region   = "$.region"
      account  = "$.account"
    }

    input_template = jsonencode({
      environment = each.key
      pipeline    = "<pipeline>"
      state       = "<state>"
      time        = "<time>"
      region      = "<region>"
      account     = "<account>"
      project     = var.project_name
      urls = {
        pipeline_console = "https://console.aws.amazon.com/codesuite/codepipeline/pipelines/<pipeline>/view"
        logs_console     = "https://console.aws.amazon.com/cloudwatch/home?region=<region>#logsV2:log-groups"
      }
    })
  }
}

# Route53 Hosted Zone (if domain is provided)
data "aws_route53_zone" "main" {
  count        = var.create_custom_domain ? 1 : 0
  name         = var.domain_name
  private_zone = false
}

# Create new wildcard ACM Certificate
resource "aws_acm_certificate" "wildcard_cert" {
  count = var.create_custom_domain ? 1 : 0

  domain_name               = "*.${var.domain_name}"
  subject_alternative_names = [var.domain_name]
  validation_method         = "DNS"

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Name        = "${var.project_name}-wildcard-cert"
    Environment = "all"
  }
}

# DNS validation records already exist - not managing them with Terraform
# Certificate will be validated by existing DNS records

# Custom Domain Records for Backend API (enabled environments only)
resource "aws_route53_record" "api" {
  for_each = var.create_custom_domain ? toset(var.enabled_environments) : []

  zone_id = data.aws_route53_zone.main[0].zone_id
  name    = "${var.environment_config[each.key].subdomain}.${var.domain_name}"
  type    = "CNAME"
  ttl     = 300
  records = [aws_elastic_beanstalk_environment.app_env[each.key].cname]
}

# Custom Domain Record for Frontend (Conditional)
resource "aws_route53_record" "frontend" {
  count = var.create_custom_domain && var.enable_frontend ? 1 : 0

  zone_id = data.aws_route53_zone.main[0].zone_id
  name    = "www.${var.domain_name}"
  type    = "CNAME"
  ttl     = 300
  records = [aws_elastic_beanstalk_environment.frontend_env[0].cname]
}

# IAM Role for CloudWatch Events to publish to SNS (Optional)
resource "aws_iam_role" "events_role" {
  count = var.enable_notifications ? 1 : 0

  name = "${var.project_name}-events-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "events.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name    = "${var.project_name}-events-role"
    Project = var.project_name
  }
}

# IAM Policy for CloudWatch Events to publish to SNS (Optional)
resource "aws_iam_role_policy" "events_sns_policy" {
  count = var.enable_notifications ? 1 : 0

  name = "${var.project_name}-events-sns-policy"
  role = aws_iam_role.events_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = aws_sns_topic.build_notifications[0].arn
      }
    ]
  })
}

# SNS Topic Policy to allow CloudWatch Events (Optional)
resource "aws_sns_topic_policy" "build_notifications_policy" {
  count = var.enable_notifications ? 1 : 0

  arn = aws_sns_topic.build_notifications[0].arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "events.amazonaws.com"
        }
        Action   = "sns:Publish"
        Resource = aws_sns_topic.build_notifications[0].arn
      }
    ]
  })
}
