# Portfolio Infrastructure

This repository contains the Terraform infrastructure code for deploying a multi-environment portfolio application on AWS with CodePipeline CI/CD.

## 🏗️ Architecture

### **4 Pipeline Setup:**
- **3 Backend Pipelines**: Auto-deploy on branch push (dev, stage, prod)
- **1 Frontend Pipeline**: Creates versioned builds in S3 for manual deployment

### **Infrastructure Components:**
- **Elastic Beanstalk**: Applications for backend services and frontend
- **CodePipeline**: CI/CD pipelines with GitHub integration
- **CodeBuild**: Build automation with comprehensive logging
- **S3**: Artifact storage and frontend version management
- **Route53**: DNS management with custom domains
- **ACM**: SSL certificates for HTTPS
- **Cost Optimized**: t2.micro instances, basic health monitoring

## 🚀 Deployment Workflow

### **Backend (Automatic):**
```
GitHub Push → Auto-trigger → CodeBuild → Deploy to EB Environment
```

### **Frontend (GitHub Actions + Manual Deployment):**
```
GitHub Push → GitHub Actions → Build Version → Upload to S3
Manual Selection → CodePipeline → Deploy to Elastic Beanstalk
```

## 📋 Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **Terraform** installed (version >= 1.0)
3. **GitHub connection** established in AWS CodeStar
4. **Domain** configured in Route53 (optional)
5. **GitHub repositories** created:
   - `learning-orgs/portfolio-backend`
   - `learning-orgs/portfolio-frontend`

## 🛠️ Setup

### **1. Infrastructure Deployment:**
```bash
# Clone this repository
git clone <repository-url>
cd portfolio-infrastructure

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy infrastructure
terraform init
terraform plan
terraform apply
```

### **2. Frontend Repository Setup:**
1. **Copy GitHub Action workflow** from `frontend-build-action.yml` to `.github/workflows/build.yml` in your frontend repository
2. **Add AWS credentials** to GitHub repository secrets:
   - `AWS_ACCESS_KEY_ID`
   - `AWS_SECRET_ACCESS_KEY`

### **3. Frontend Version Creation:**
```bash
# Automatic version (push to main branch)
git push origin main

# Manual version creation
./create-version.sh 1.0.5
./create-version.sh 1.1.0 staging
```

### **4. Frontend Deployment:**
```bash
# List and select version interactively
./deploy-frontend-version.sh

# Deploy specific version
./deploy-frontend-version.sh portfolio-frontend-v1.0.1-********
```

## 📊 S3 Version Management

- **Location**: `s3://portfolio-app-cicd-artifacts/frontend-versions/`
- **Format**: `portfolio-frontend-v1.0.1-********.zip`
- **Metadata**: `portfolio-frontend-v1.0.1-********-info.json`
- **Versioning**: Semantic versioning (major.minor.patch)
- **Cleanup**: Automatically keeps last 10 versions
- **Rollback**: Deploy any stored version anytime

### **Version Examples:**
- `portfolio-frontend-v1.0.1-********.zip` - First release
- `portfolio-frontend-v1.0.2-********.zip` - Bug fix
- `portfolio-frontend-v1.1.0-********.zip` - New feature
- `portfolio-frontend-v2.0.0-********.zip` - Major release

## 🔧 Configuration

### **Required Variables** (terraform.tfvars):
```hcl
project_name = "portfolio-app"
github_owner = "your-github-username"
github_connection_arn = "arn:aws:codestar-connections:region:account:connection/xxx"
notification_email = "<EMAIL>"

# Optional - for custom domain
domain_name = "yourdomain.com"
```

### **Environment URLs:**
- **Dev**: `https://dev-api.yourdomain.com`
- **Stage**: `https://stage-api.yourdomain.com`
- **Prod**: `https://api.yourdomain.com`
- **Frontend**: `https://www.yourdomain.com`

## 📁 Repository Structure

```
portfolio-infrastructure/
├── main.tf                          # Main infrastructure code
├── variables.tf                     # Variable definitions
├── outputs.tf                       # Output definitions
├── terraform.tfvars.example         # Example configuration
├── buildspec-versioned.yml          # Frontend buildspec template
├── deploy-frontend-version.sh       # Frontend deployment script
└── README.md                        # This file
```

## 🎯 Key Features

- ✅ **Cost Optimized**: t2.micro instances, console logging
- ✅ **Auto-Deploy Backend**: Push to branch = automatic deployment
- ✅ **Version Control Frontend**: Manual deployment with version selection
- ✅ **Rollback Capability**: Deploy any previous version
- ✅ **SSL/HTTPS**: Automatic certificate management
- ✅ **Multi-Environment**: Dev, Stage, Production environments
- ✅ **Comprehensive Logging**: Detailed build and deployment logs

## 🔍 Monitoring

- **Pipeline Status**: AWS CodePipeline Console
- **Build Logs**: AWS CodeBuild Console
- **Application Health**: Elastic Beanstalk Console
- **Email Notifications**: SNS notifications for pipeline events

## 🆘 Troubleshooting

### **Frontend Deployment Issues:**
1. Check S3 for available versions
2. Verify buildspec.yml in frontend repository
3. Check CodeBuild logs for build errors

### **Backend Deployment Issues:**
1. Check pipeline status in CodePipeline
2. Verify branch names match pipeline configuration
3. Check Elastic Beanstalk environment health

## 📞 Support

For issues or questions:
1. Check AWS CloudWatch logs
2. Review CodeBuild build logs
3. Verify Terraform state consistency
