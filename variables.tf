# Terraform Variables Configuration for Portfolio Infrastructure
#
# This file defines all configurable parameters for the portfolio application infrastructure
# including both Spring Boot backend and React frontend deployments.
#
# Key Features:
# - Multi-environment backend support (dev/stage/prod)
# - Single production frontend deployment for cost optimization
# - GitHub integration with AWS Secrets Manager for secure token storage
# - Configurable backend connectivity for frontend
# - Custom domain support with SSL certificates

# AWS Configuration
# Specifies the AWS region where all resources will be deployed
variable "aws_region" {
  description = "AWS region for deployment (us-east-1 recommended for cost optimization)"
  type        = string
  default     = "us-east-1"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.aws_region))
    error_message = "AWS region must be a valid region identifier."
  }
}

# Project Configuration
# Used as prefix for all AWS resource names to ensure uniqueness
variable "project_name" {
  description = "Name of the project - used as prefix for all AWS resources"
  type        = string
  default     = "portfolio-app"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "Project name must contain only lowercase letters, numbers, and hyphens."
  }
}

# Environment Configuration
# List of environments to create (dev, stage, prod)
variable "environments" {
  description = "List of environments to create"
  type        = list(string)
  default     = ["dev", "stage", "prod"]

  validation {
    condition = alltrue([
      for env in var.environments : contains(["dev", "stage", "prod"], env)
    ])
    error_message = "Environments must be one of: dev, stage, prod."
  }
}

# GitHub Repository Configuration
variable "github_repo" {
  description = "GitHub repository name (just the repo name, not owner/repo)"
  type        = string
  default     = "my-portfolio-app"
}

# Instance Type Configuration
# EC2 instance type for Elastic Beanstalk environments
variable "instance_type" {
  description = "EC2 instance type for Elastic Beanstalk environments"
  type        = string
  default     = "t3.micro"

  validation {
    condition = can(regex("^[a-z][0-9][a-z]?\\.(nano|micro|small|medium|large|xlarge|[0-9]+xlarge)$", var.instance_type))
    error_message = "Instance type must be a valid EC2 instance type."
  }
}

# Environment Control Configuration
variable "enabled_environments" {
  description = "List of environments to create (dev, stage, prod). Remove environments to save costs."
  type        = list(string)
  default     = ["prod"]  # Only prod by default for cost optimization

  validation {
    condition = alltrue([
      for env in var.enabled_environments : contains(["dev", "stage", "prod"], env)
    ])
    error_message = "Enabled environments must be one of: dev, stage, prod."
  }
}

variable "enable_frontend" {
  description = "Enable frontend environment creation"
  type        = bool
  default     = true
}

# Cost Optimization Configuration
variable "enable_spot_instances" {
  description = "Enable spot instances for non-production environments (60-90% cost savings)"
  type        = bool
  default     = true
}

variable "spot_max_price" {
  description = "Maximum price for spot instances (USD per hour)"
  type        = string
  default     = "0.05"
}

variable "auto_scaling_max_size" {
  description = "Maximum number of instances in auto scaling group per environment"
  type        = map(number)
  default = {
    dev   = 1  # Dev: Single instance only
    stage = 1  # Stage: Single instance only
    prod  = 3  # Prod: Can scale up to 3 instances
  }
}

variable "enable_lifecycle_policy" {
  description = "Enable S3 lifecycle policy for automatic cleanup and cost optimization"
  type        = bool
  default     = true
}

# Domain Configuration
# Whether to create custom domain and SSL certificate
variable "create_custom_domain" {
  description = "Whether to create custom domain and SSL certificate"
  type        = bool
  default     = true
}

# Domain Name
# The domain name for custom domain setup
variable "domain_name" {
  description = "Domain name for custom domain setup"
  type        = string
  default     = "manojpatidar.in"

  validation {
    condition = can(regex("^[a-z0-9.-]+\\.[a-z]{2,}$", var.domain_name))
    error_message = "Domain name must be a valid domain format."
  }
}

# Environment-specific configuration
variable "environment_config" {
  description = "Configuration for each environment"
  type = map(object({
    subdomain          = string
    frontend_subdomain = string
    branch            = string
  }))
  default = {
    dev = {
      subdomain          = "dev-api"
      frontend_subdomain = "dev"
      branch            = "dev"
    }
    stage = {
      subdomain          = "stage-api"
      frontend_subdomain = "stage"
      branch            = "stage"
    }
    prod = {
      subdomain          = "api"
      frontend_subdomain = "www"
      branch            = "main"
    }
  }
}

# Notification Email
variable "notification_email" {
  description = "Email address for build notifications"
  type        = string
  default     = "<EMAIL>"

  validation {
    condition = can(regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", var.notification_email))
    error_message = "Notification email must be a valid email address."
  }
}

# Frontend-specific variables
variable "github_owner" {
  description = "GitHub repository owner/organization"
  type        = string
  default     = "learning-orgs"
}

variable "github_branch_frontend" {
  description = "GitHub branch for frontend deployment"
  type        = string
  default     = "main"
}

variable "frontend_api_url" {
  description = "API URL for frontend to connect to (configurable during deployment)"
  type        = string
  default     = ""
}

variable "backend_environment" {
  description = "Backend environment to connect to (dev/stage/prod)"
  type        = string
  default     = "prod"
  validation {
    condition     = contains(["dev", "stage", "prod"], var.backend_environment)
    error_message = "Backend environment must be one of: dev, stage, prod."
  }
}

# GitHub CodeStar Connection ARN (created manually)
variable "github_connection_arn" {
  description = "ARN of the manually created CodeStar connection for GitHub"
  type        = string
  default     = ""

  validation {
    condition = can(regex("^arn:aws:(codestar-connections|codeconnections):", var.github_connection_arn)) || var.github_connection_arn == ""
    error_message = "GitHub connection ARN must be a valid CodeStar/CodeConnections ARN or empty string."
  }
}