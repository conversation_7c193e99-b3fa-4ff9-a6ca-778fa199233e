# 🔧 Configuration Constants for Portfolio Infrastructure
# This file contains all configurable constants and presets for the infrastructure

locals {
  # ========================================
  # BUILD RETENTION CONSTANTS
  # ========================================
  
  # Default build retention settings
  build_retention = {
    default_versions = 3    # Default number of builds to keep
    min_versions     = 1    # Minimum builds to keep
    max_versions     = 10   # Maximum builds to keep
    
    # GitHub Actions compatible settings
    github_actions_compatible = 3  # Optimal for CI/CD workflows
  }
  
  # S3 Cleanup intervals (days)
  cleanup_intervals = {
    aggressive = 1    # Delete after 1 day (maximum savings)
    standard   = 7    # Delete after 7 days (balanced)
    relaxed    = 30   # Delete after 30 days (safe retention)
    extended   = 90   # Delete after 90 days (long retention)
  }
  
  # ========================================
  # COST OPTIMIZATION PRESETS
  # ========================================
  
  cost_presets = {
    # Tier 1: Maximum Savings ($0/month)
    maximum_savings = {
      enabled_environments   = []
      enable_frontend       = false
      enable_s3_storage     = false
      max_build_versions    = 0
      s3_cleanup_days       = 1
      description          = "Everything disabled - $0/month"
      estimated_cost       = "$0"
    }
    
    # Tier 2: Minimal Setup ($25-75/month)
    minimal_setup = {
      enabled_environments   = ["prod"]
      enable_frontend       = false
      enable_s3_storage     = true
      max_build_versions    = local.build_retention.default_versions
      s3_cleanup_days       = local.cleanup_intervals.standard
      description          = "Production backend only"
      estimated_cost       = "$25-75"
    }
    
    # Tier 3: Standard Setup ($75-105/month)
    standard_setup = {
      enabled_environments   = ["prod"]
      enable_frontend       = true
      enable_s3_storage     = true
      max_build_versions    = local.build_retention.default_versions
      s3_cleanup_days       = local.cleanup_intervals.standard
      description          = "Production backend + frontend"
      estimated_cost       = "$75-105"
    }
    
    # Tier 4: Development Mode ($50-135/month)
    development_mode = {
      enabled_environments   = ["dev", "stage", "prod"]
      enable_frontend       = true
      enable_s3_storage     = true
      max_build_versions    = local.build_retention.default_versions
      s3_cleanup_days       = local.cleanup_intervals.standard
      enable_spot_instances = true
      description          = "All environments with spot instances"
      estimated_cost       = "$50-135"
    }
  }
  
  # ========================================
  # ENVIRONMENT CONFIGURATIONS
  # ========================================
  
  environment_configs = {
    dev = {
      instance_type         = "t2.micro"
      min_size             = 1
      max_size             = 1
      spot_enabled         = true
      spot_max_price       = "0.05"
      health_check_type    = "basic"
      auto_scaling_trigger = "CPUUtilization"
      scale_up_threshold   = 80
      scale_down_threshold = 20
      description          = "Development environment with spot instances"
    }
    
    stage = {
      instance_type         = "t2.micro"
      min_size             = 1
      max_size             = 1
      spot_enabled         = true
      spot_max_price       = "0.05"
      health_check_type    = "basic"
      auto_scaling_trigger = "CPUUtilization"
      scale_up_threshold   = 80
      scale_down_threshold = 20
      description          = "Staging environment with spot instances"
    }
    
    prod = {
      instance_type         = "t2.micro"
      min_size             = 1
      max_size             = 3
      spot_enabled         = false
      spot_max_price       = null
      health_check_type    = "basic"
      auto_scaling_trigger = "CPUUtilization"
      scale_up_threshold   = 70
      scale_down_threshold = 30
      description          = "Production environment with on-demand instances"
    }
    
    frontend = {
      instance_type         = "t2.micro"
      min_size             = 1
      max_size             = 2
      spot_enabled         = false
      spot_max_price       = null
      health_check_type    = "basic"
      auto_scaling_trigger = "CPUUtilization"
      scale_up_threshold   = 75
      scale_down_threshold = 25
      description          = "Frontend environment for React/Node.js"
    }
  }
  
  # ========================================
  # INSTANCE TYPE CONFIGURATIONS
  # ========================================
  
  instance_types = {
    # Cost-optimized options
    micro = {
      type = "t2.micro"
      vcpu = 1
      memory = "1 GiB"
      cost_per_hour = "$0.0116"
      use_case = "Development, light workloads"
    }
    
    small = {
      type = "t3.small"
      vcpu = 2
      memory = "2 GiB"
      cost_per_hour = "$0.0208"
      use_case = "Small production workloads"
    }
    
    medium = {
      type = "t3.medium"
      vcpu = 2
      memory = "4 GiB"
      cost_per_hour = "$0.0416"
      use_case = "Medium production workloads"
    }
  }
  
  # ========================================
  # S3 LIFECYCLE CONFIGURATIONS
  # ========================================
  
  s3_lifecycle_rules = {
    # Aggressive cleanup for maximum cost savings
    aggressive = {
      cleanup_days = local.cleanup_intervals.aggressive
      max_versions = 1
      abort_incomplete_uploads_days = 1
      description = "Aggressive cleanup - maximum savings"
    }
    
    # Standard cleanup for balanced cost/safety
    standard = {
      cleanup_days = local.cleanup_intervals.standard
      max_versions = local.build_retention.default_versions
      abort_incomplete_uploads_days = 1
      description = "Standard cleanup - balanced approach"
    }
    
    # Relaxed cleanup for safety
    relaxed = {
      cleanup_days = local.cleanup_intervals.relaxed
      max_versions = local.build_retention.max_versions
      abort_incomplete_uploads_days = 7
      description = "Relaxed cleanup - safety first"
    }
  }
  
  # ========================================
  # MONITORING AND ALERTING
  # ========================================
  
  monitoring_configs = {
    basic = {
      health_check_interval = 30
      health_check_timeout = 5
      unhealthy_threshold = 5
      healthy_threshold = 3
      cloudwatch_logs = false
      detailed_monitoring = false
    }
    
    enhanced = {
      health_check_interval = 15
      health_check_timeout = 5
      unhealthy_threshold = 3
      healthy_threshold = 2
      cloudwatch_logs = true
      detailed_monitoring = true
    }
  }
  
  # ========================================
  # COST TRACKING TAGS
  # ========================================
  
  cost_tracking_tags = {
    CostCenter = "Development"
    Owner = "DevOps"
    Project = var.project_name
    ManagedBy = "Terraform"
    Purpose = "Multi-Environment-Pipeline"
  }
  
  # Environment-specific cost tags
  environment_cost_tags = {
    dev = merge(local.cost_tracking_tags, {
      Environment = "dev"
      CostCategory = "Development"
      BillingCode = "DEV-001"
    })
    
    stage = merge(local.cost_tracking_tags, {
      Environment = "stage"
      CostCategory = "Testing"
      BillingCode = "STG-001"
    })
    
    prod = merge(local.cost_tracking_tags, {
      Environment = "prod"
      CostCategory = "Production"
      BillingCode = "PRD-001"
    })
    
    frontend = merge(local.cost_tracking_tags, {
      Environment = "production"
      Type = "Frontend"
      CostCategory = "Production"
      BillingCode = "FE-001"
    })
  }
  
  # ========================================
  # VALIDATION RULES
  # ========================================
  
  validation_rules = {
    max_build_versions = {
      min = local.build_retention.min_versions
      max = local.build_retention.max_versions
      default = local.build_retention.default_versions
    }
    
    s3_cleanup_days = {
      min = 1
      max = 365
      default = local.cleanup_intervals.standard
    }
    
    spot_max_price = {
      min = "0.01"
      max = "1.00"
      default = "0.05"
    }
  }
}

# ========================================
# COMPUTED VALUES
# ========================================

# Current configuration preset (if using presets)
locals {
  current_preset = var.use_cost_preset != "" ? local.cost_presets[var.use_cost_preset] : null
  
  # Effective configuration (preset or custom)
  effective_config = local.current_preset != null ? local.current_preset : {
    enabled_environments = var.enabled_environments
    enable_frontend = var.enable_frontend
    enable_s3_storage = var.enable_s3_storage
    max_build_versions = var.max_build_versions
    s3_cleanup_days = var.s3_cleanup_days
  }
  
  # Cost estimation based on current configuration
  estimated_monthly_cost = {
    dev_cost = contains(local.effective_config.enabled_environments, "dev") ? 
      (var.enable_spot_instances ? 10 : 20) : 0
    stage_cost = contains(local.effective_config.enabled_environments, "stage") ? 
      (var.enable_spot_instances ? 10 : 20) : 0
    prod_cost = contains(local.effective_config.enabled_environments, "prod") ? 50 : 0
    frontend_cost = local.effective_config.enable_frontend ? 20 : 0
    s3_cost = local.effective_config.enable_s3_storage ? 5 : 0
    
    total_min = 5 + 
      (contains(local.effective_config.enabled_environments, "dev") ? (var.enable_spot_instances ? 5 : 15) : 0) +
      (contains(local.effective_config.enabled_environments, "stage") ? (var.enable_spot_instances ? 5 : 15) : 0) +
      (contains(local.effective_config.enabled_environments, "prod") ? 25 : 0) +
      (local.effective_config.enable_frontend ? 15 : 0) +
      (local.effective_config.enable_s3_storage ? 1 : 0)
      
    total_max = 5 + 
      (contains(local.effective_config.enabled_environments, "dev") ? (var.enable_spot_instances ? 15 : 25) : 0) +
      (contains(local.effective_config.enabled_environments, "stage") ? (var.enable_spot_instances ? 15 : 25) : 0) +
      (contains(local.effective_config.enabled_environments, "prod") ? 75 : 0) +
      (local.effective_config.enable_frontend ? 25 : 0) +
      (local.effective_config.enable_s3_storage ? 5 : 0)
  }
}
